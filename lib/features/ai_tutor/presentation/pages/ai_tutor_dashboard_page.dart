import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import '../../../../models/usage.dart';
import '../../../../widgets/enhanced_app_bar.dart';
import '../bloc/ai_tutor_bloc.dart';
import '../widgets/learning_plan_widget.dart';

/// Main dashboard page for the AI Tutor feature
class AITutorDashboardPage extends StatefulWidget {
  final String title;
  final Usage type;

  const AITutorDashboardPage({
    super.key,
    required this.title,
    required this.type,
  });

  @override
  State<AITutorDashboardPage> createState() => _AITutorDashboardPageState();
}

class _AITutorDashboardPageState extends State<AITutorDashboardPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Form state variables
  String? _selectedSubject;
  String? _selectedLevel;
  final List<String> _learningGoals = [];
  final TextEditingController _goalController = TextEditingController();

  // Available options
  final List<String> _subjects = [
    'Mathematics',
    'Science',
    'History',
    'Literature',
    'Computer Science',
    'Languages',
    'Art',
    'Music',
  ];

  final List<String> _levels = [
    'Beginner',
    'Intermediate',
    'Advanced',
    'Expert',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  // Mock data methods - these would be replaced with actual state data
  Map<String, dynamic> _getMockStats() {
    return {
      'totalCards': 42,
      'dueToday': 8,
      'mastered': 15,
      'completedQuizzes': 12,
      'averageScore': 85,
      'currentStreak': 7,
      'studyTime': '2h 30m',
      'conceptsLearned': 23,
      'accuracyRate': 87,
      'knowledgeGaps': 3,
    };
  }

  @override
  void dispose() {
    _tabController.dispose();
    _goalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<AITutorBloc>(),
      child: Scaffold(
        appBar: EnhancedAppBar(
          title: widget.title,
          showBackButton: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.analytics),
              onPressed: () => _navigateToAnalytics(),
              tooltip: 'Learning Analytics',
            ),
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () => _navigateToSettings(),
              tooltip: 'Settings',
            ),
          ],
        ),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildLearningPlanTab(),
                  _buildFlashcardsTab(),
                  _buildQuizTab(),
                  _buildProgressTab(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(Icons.map), text: 'Learning Plan'),
          Tab(icon: Icon(Icons.style), text: 'Flashcards'),
          Tab(icon: Icon(Icons.quiz), text: 'Quiz'),
          Tab(icon: Icon(Icons.trending_up), text: 'Progress'),
        ],
        labelColor: Theme.of(context).colorScheme.primary,
        unselectedLabelColor: Theme.of(
          context,
        ).colorScheme.onSurface.withValues(alpha: 0.6),
        indicatorColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildLearningPlanTab() {
    return BlocBuilder<AITutorBloc, AITutorState>(
      builder: (context, state) {
        if (state is AITutorInitial) {
          return _buildLearningPlanSetup();
        } else if (state is AITutorLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Generating your personalized learning plan...'),
              ],
            ),
          );
        } else if (state is LearningPlanGenerated) {
          return LearningPlanWidget(learningPlan: state.learningPlan);
        } else if (state is AITutorError) {
          return _buildErrorWidget(state.message);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildLearningPlanSetup() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 24),
          _buildQuickStartCard(),
          const SizedBox(height: 24),
          _buildFeaturesOverview(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.school,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Welcome to AI Tutor',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Your personalized learning companion powered by AI. Create custom learning plans, practice with flashcards, take adaptive quizzes, and track your progress.',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStartCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Start',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildSubjectSelector(),
            const SizedBox(height: 16),
            _buildLevelSelector(),
            const SizedBox(height: 16),
            _buildGoalsInput(),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _generateLearningPlan,
                icon: const Icon(Icons.auto_awesome),
                label: const Text('Generate Learning Plan'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesOverview() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Features',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildFeatureItem(
              icon: Icons.map,
              title: 'Personalized Learning Plans',
              description:
                  'AI-generated study plans tailored to your goals and learning style.',
            ),
            _buildFeatureItem(
              icon: Icons.style,
              title: 'Smart Flashcards',
              description:
                  'Spaced repetition system that adapts to your memory patterns.',
            ),
            _buildFeatureItem(
              icon: Icons.quiz,
              title: 'Adaptive Quizzes',
              description:
                  'Dynamic quizzes that adjust difficulty based on your performance.',
            ),
            _buildFeatureItem(
              icon: Icons.trending_up,
              title: 'Progress Tracking',
              description:
                  'Detailed analytics to monitor your learning journey.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: Theme.of(context).colorScheme.primary, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Subject', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedSubject,
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            hintText: 'Select a subject',
            prefixIcon: const Icon(Icons.school),
          ),
          items: _subjects.map((subject) {
            return DropdownMenuItem(value: subject, child: Text(subject));
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedSubject = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select a subject';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildLevelSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Current Level', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: _levels.map((level) {
              return RadioListTile<String>(
                title: Text(level),
                value: level,
                groupValue: _selectedLevel,
                onChanged: (value) {
                  setState(() {
                    _selectedLevel = value;
                  });
                },
                dense: true,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildGoalsInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Learning Goals', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Display existing goals as chips
              if (_learningGoals.isNotEmpty) ...[
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: _learningGoals.map((goal) {
                    return Chip(
                      label: Text(goal),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          _learningGoals.remove(goal);
                        });
                      },
                    );
                  }).toList(),
                ),
                const SizedBox(height: 8),
              ],
              // Input field for new goals
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _goalController,
                      decoration: const InputDecoration(
                        hintText: 'Add a learning goal...',
                        border: InputBorder.none,
                        isDense: true,
                      ),
                      onSubmitted: _addGoal,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: () => _addGoal(_goalController.text),
                    tooltip: 'Add goal',
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _addGoal(String goal) {
    if (goal.trim().isNotEmpty && !_learningGoals.contains(goal.trim())) {
      setState(() {
        _learningGoals.add(goal.trim());
        _goalController.clear();
      });
    }
  }

  Widget _buildFlashcardsTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with create button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Flashcards',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: _showCreateFlashcardsDialog,
                icon: const Icon(Icons.add),
                label: const Text('Create Set'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Cards',
                  '${_getMockStats()['totalCards']}',
                  Icons.quiz,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Due Today',
                  '${_getMockStats()['dueToday']}',
                  Icons.schedule,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Mastered',
                  '${_getMockStats()['mastered']}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Flashcard sets list
          Expanded(child: _buildFlashcardSetsList()),
        ],
      ),
    );
  }

  Widget _buildQuizTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Quizzes',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: _showCreateQuizDialog,
                icon: const Icon(Icons.add),
                label: const Text('Create Quiz'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quiz stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Completed',
                  '${_getMockStats()['completedQuizzes']}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Average Score',
                  '${_getMockStats()['averageScore']}%',
                  Icons.trending_up,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Streak',
                  '${_getMockStats()['currentStreak']}',
                  Icons.local_fire_department,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Quiz list
          Expanded(child: _buildQuizList()),
        ],
      ),
    );
  }

  Widget _buildProgressTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Text(
            'Learning Progress',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Overall progress card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withValues(alpha: 0.7),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Overall Progress',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Study Streak',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                          Text(
                            '${_getMockStats()['currentStreak']} days',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Total Study Time',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                          Text(
                            '${_getMockStats()['studyTime']}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Progress metrics
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Concepts Learned',
                  '${_getMockStats()['conceptsLearned']}',
                  Icons.lightbulb,
                  Colors.amber,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Accuracy Rate',
                  '${_getMockStats()['accuracyRate']}%',
                  Icons.track_changes,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Knowledge Gaps',
                  '${_getMockStats()['knowledgeGaps']}',
                  Icons.warning,
                  Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Recent activity
          Expanded(child: _buildRecentActivity()),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _generateLearningPlan(),
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () => _showQuickActions(),
      icon: const Icon(Icons.add),
      label: const Text('Quick Action'),
    );
  }

  void _generateLearningPlan() {
    // Validate form data
    if (_selectedSubject == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please select a subject')));
      return;
    }

    if (_selectedLevel == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select your current level')),
      );
      return;
    }

    if (_learningGoals.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one learning goal')),
      );
      return;
    }

    // Collect form data and dispatch event
    context.read<AITutorBloc>().add(
      GenerateLearningPlanEvent(
        subject: _selectedSubject!,
        currentLevel: _selectedLevel!,
        learningGoals: List.from(_learningGoals),
        preferences: {
          'usageType': widget.type.type.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      ),
    );
  }

  void _navigateToAnalytics() {
    // Navigate to analytics page - show enhanced analytics dialog for now
    // Future enhancement: Create dedicated analytics page when needed
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.analytics, color: Colors.blue),
            SizedBox(width: 8),
            Text('Learning Analytics'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('📊 Detailed analytics coming soon!'),
            SizedBox(height: 16),
            Text('Features will include:'),
            SizedBox(height: 8),
            Text('• Progress tracking over time'),
            Text('• Performance insights'),
            Text('• Learning pattern analysis'),
            Text('• Personalized recommendations'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _navigateToSettings() {
    // Navigate to settings page - show enhanced settings dialog for now
    // Future enhancement: Create dedicated settings page when needed
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.settings, color: Colors.orange),
            SizedBox(width: 8),
            Text('AI Tutor Settings'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('⚙️ Advanced settings coming soon!'),
            SizedBox(height: 16),
            Text('Settings will include:'),
            SizedBox(height: 8),
            Text('• Learning preferences'),
            Text('• Difficulty adjustment'),
            Text('• Notification settings'),
            Text('• Study reminders'),
            Text('• AI model selection'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.style),
              title: const Text('Create Flashcards'),
              onTap: () {
                Navigator.pop(context);
                _showFeatureComingSoon('Flashcard Creation');
              },
            ),
            ListTile(
              leading: const Icon(Icons.quiz),
              title: const Text('Take Quiz'),
              onTap: () {
                Navigator.pop(context);
                _showFeatureComingSoon('Quiz Interface');
              },
            ),
            ListTile(
              leading: const Icon(Icons.school),
              title: const Text('Study Session'),
              onTap: () {
                Navigator.pop(context);
                _showFeatureComingSoon('Study Session');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFeatureComingSoon(String featureName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$featureName coming soon!'),
        backgroundColor: Colors.blue,
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // Helper methods for flashcards tab
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFlashcardSetsList() {
    // Using mock data - replace with actual flashcard sets from BLoC state
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.style, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No flashcard sets yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first flashcard set to start studying',
            style: TextStyle(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showCreateFlashcardsDialog,
            icon: const Icon(Icons.add),
            label: const Text('Create Flashcard Set'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateFlashcardsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Flashcard Set'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'Topic',
                hintText: 'e.g., Spanish Vocabulary',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Number of cards',
                hintText: '10',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showFeatureComingSoon('Flashcard Creation');
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  // Helper methods for quiz tab
  Widget _buildQuizList() {
    // Using mock data - replace with actual quiz data from BLoC state
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.quiz, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No quizzes yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first quiz to test your knowledge',
            style: TextStyle(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showCreateQuizDialog,
            icon: const Icon(Icons.add),
            label: const Text('Create Quiz'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateQuizDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Quiz'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'Topic',
                hintText: 'e.g., World History',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Number of questions',
                hintText: '5',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Difficulty',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'easy', child: Text('Easy')),
                DropdownMenuItem(value: 'medium', child: Text('Medium')),
                DropdownMenuItem(value: 'hard', child: Text('Hard')),
              ],
              onChanged: (value) {},
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showFeatureComingSoon('Quiz Creation');
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  // Helper method for progress tab
  Widget _buildRecentActivity() {
    // Using mock data - replace with actual recent activity from BLoC state
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Activity',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.history, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 12),
                Text(
                  'No recent activity',
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'Start studying to see your activity here',
                  style: TextStyle(color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
