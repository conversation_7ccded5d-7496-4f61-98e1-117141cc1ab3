import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:diogeneschatbot/features/ai_tutor/presentation/bloc/ai_tutor_bloc.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/services/service_locator.dart';
import '../widgets/progress_chart_widget.dart';
import '../../domain/entities/learning_progress.dart';
import '../../domain/repositories/ai_tutor_repository.dart';
import '../../data/repositories/mock_ai_tutor_repository.dart';
import '../../domain/services/ai_content_service.dart';

/// Main AI Tutor page with comprehensive learning features
/// Features include: learning plans, flashcards, quizzes, and progress tracking
/// Future enhancements: personalized recommendations, adaptive difficulty,
/// social learning features, calendar integration, and gamification elements
class AITutorPage extends StatefulWidget {
  const AITutorPage({super.key});

  @override
  State<AITutorPage> createState() => _AITutorPageState();
}

class _AITutorPageState extends State<AITutorPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;
  late AITutorRepository _repository;

  // Progress data state
  LearningProgress? _userProgress;
  bool _isLoadingProgress = false;
  String _totalStudyTime = '0h 0m';
  String _averageScore = '0%';
  String _studyStreak = '0 days';
  String _conceptsMastered = '0';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentIndex = _tabController.index;
      });
    });
    _repository = MockAITutorRepository(aiContentService: AIContentService());
    _loadUserProgress();
  }

  /// Loads user progress data from repository
  Future<void> _loadUserProgress() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    setState(() {
      _isLoadingProgress = true;
    });

    try {
      final progressResult = await _repository.getLearningProgress(
        userId: user.uid,
        subject: 'General', // Can be made dynamic based on user selection
      );

      progressResult.fold(
        (failure) {
          // Use default values on failure
          _updateProgressStats(null);
        },
        (progress) {
          _updateProgressStats(progress);
        },
      );
    } catch (e) {
      _updateProgressStats(null);
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingProgress = false;
        });
      }
    }
  }

  /// Updates progress statistics from learning progress data
  void _updateProgressStats(LearningProgress? progress) {
    if (progress == null) {
      // Use mock data when no real data is available
      _totalStudyTime = '24h 30m';
      _averageScore = '87%';
      _studyStreak = '12 days';
      _conceptsMastered = '23';
      return;
    }

    _userProgress = progress;

    // Calculate total study time
    final totalMinutes = progress.stats.totalStudyTime;
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;
    _totalStudyTime = '${hours}h ${minutes}m';

    // Format average score
    _averageScore = '${progress.stats.averageQuizScore.toStringAsFixed(0)}%';

    // Format study streak
    _studyStreak = '${progress.stats.streakDays} days';

    // Count mastered concepts
    _conceptsMastered = progress.masteredConcepts.length.toString();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AITutorBloc>(),
      child: Scaffold(
        appBar: EnhancedAppBar(
          title: 'AI Tutor',
          actions: [
            IconButton(
              icon: const Icon(Icons.settings, color: Colors.white),
              onPressed: () => _showSettingsDialog(context),
              tooltip: 'Tutor Settings',
            ),
            IconButton(
              icon: const Icon(Icons.help_outline, color: Colors.white),
              onPressed: () => _showHelpDialog(context),
              tooltip: 'Help & Tips',
            ),
          ],
        ),
        body: Column(
          children: [
            // Tab bar with enhanced styling
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: AppTheme.primaryGreen,
                unselectedLabelColor: Colors.grey,
                indicatorColor: AppTheme.primaryGreen,
                indicatorWeight: 3,
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
                tabs: const [
                  Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
                  Tab(icon: Icon(Icons.school), text: 'Learn'),
                  Tab(icon: Icon(Icons.quiz), text: 'Practice'),
                  Tab(icon: Icon(Icons.analytics), text: 'Progress'),
                ],
              ),
            ),
            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildDashboardTab(),
                  _buildLearningTab(),
                  _buildPracticeTab(),
                  _buildProgressTab(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome section
          EnhancedCard(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.school,
                          color: AppTheme.primaryGreen,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome to AI Tutor!',
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryGreen,
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Your personalized learning journey starts here',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // Quick stats
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'Study Streak',
                          '7 days', // Mock data - will be replaced with real user data
                          Icons.local_fire_department,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'Cards Reviewed',
                          '42', // Mock data - will be replaced with real user data
                          Icons.style,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'Quizzes Taken',
                          '8', // Mock data - will be replaced with real user data
                          Icons.quiz,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Quick actions
          Text(
            'Quick Actions',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  'Create Learning Plan',
                  'Start a new subject',
                  Icons.add_circle,
                  AppTheme.primaryGreen,
                  () => _tabController.animateTo(1),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionCard(
                  'Practice Flashcards',
                  'Review your cards',
                  Icons.style,
                  Colors.blue,
                  () => _tabController.animateTo(2),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  'Take Quiz',
                  'Test your knowledge',
                  Icons.quiz,
                  Colors.purple,
                  () => _tabController.animateTo(2),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionCard(
                  'View Progress',
                  'Track your learning',
                  Icons.analytics,
                  Colors.orange,
                  () => _tabController.animateTo(3),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLearningTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.school, size: 64, color: AppTheme.primaryGreen),
          const SizedBox(height: 16),
          Text(
            'Learning Plans',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Create personalized learning plans\nwith AI assistance',
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showCreatePlanDialog,
            icon: const Icon(Icons.add),
            label: const Text('Create Learning Plan'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPracticeTab() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: const TabBar(
              labelColor: AppTheme.primaryGreen,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppTheme.primaryGreen,
              tabs: [
                Tab(text: 'Flashcards'),
                Tab(text: 'Quizzes'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [_buildFlashcardTab(), _buildQuizTab()],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlashcardTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.style, size: 64, color: Colors.blue),
          const SizedBox(height: 16),
          Text(
            'Flashcards',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Review flashcards with spaced repetition\nfor optimal learning',
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showPracticeOptions(),
            icon: const Icon(Icons.play_arrow),
            label: const Text('Start Flashcard Review'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuizTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.quiz, size: 64, color: Colors.purple),
          const SizedBox(height: 16),
          Text(
            'Quizzes',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Test your knowledge with\nAI-generated adaptive quizzes',
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showPracticeOptions(),
            icon: const Icon(Icons.play_arrow),
            label: const Text('Take Quiz'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress overview header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress Analytics',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  // Filter button
                  IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: () => _showProgressFilters(),
                    tooltip: 'Filter progress data',
                  ),
                  // Export button
                  IconButton(
                    icon: const Icon(Icons.download),
                    onPressed: () => _exportProgressData(),
                    tooltip: 'Export progress data',
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick stats cards
          Row(
            children: [
              Expanded(
                child: _buildProgressStatCard(
                  'Total Study Time',
                  _isLoadingProgress ? 'Loading...' : _totalStudyTime,
                  Icons.access_time,
                  Colors.blue,
                  'This week',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildProgressStatCard(
                  'Average Score',
                  _isLoadingProgress ? 'Loading...' : _averageScore,
                  Icons.trending_up,
                  Colors.green,
                  '+5% from last week',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildProgressStatCard(
                  'Study Streak',
                  _isLoadingProgress ? 'Loading...' : _studyStreak,
                  Icons.local_fire_department,
                  Colors.orange,
                  'Personal best!',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildProgressStatCard(
                  'Concepts Mastered',
                  _isLoadingProgress ? 'Loading...' : _conceptsMastered,
                  Icons.psychology,
                  Colors.purple,
                  '3 this week',
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Progress chart section
          Text(
            'Learning Progress Chart',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          // Enhanced ProgressChartWidget with real data integration
          ProgressChartWidget(
            title: 'Learning Progress Chart',
            subject: _userProgress?.subject ?? 'General',
            topic: _userProgress?.topic,
          ),
          const SizedBox(height: 24),

          // Recent activity section
          Text(
            'Recent Activity',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          _buildRecentActivityList(),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_currentIndex) {
      case 1: // Learning tab
        return FloatingActionButton.extended(
          onPressed: () => _showCreatePlanDialog(),
          icon: const Icon(Icons.add),
          label: const Text('New Plan'),
          backgroundColor: AppTheme.primaryGreen,
        );
      case 2: // Practice tab
        return FloatingActionButton.extended(
          onPressed: () => _showPracticeOptions(),
          icon: const Icon(Icons.play_arrow),
          label: const Text('Start Practice'),
          backgroundColor: AppTheme.primaryGreen,
        );
      default:
        return null;
    }
  }

  void _showSettingsDialog(BuildContext context) {
    // Enhanced settings dialog with user-friendly options
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.settings, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('AI Tutor Settings'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.notifications),
                title: const Text('Study Reminders'),
                subtitle: const Text('Get notified about study sessions'),
                trailing: Switch(
                  value: true, // Mock value
                  onChanged: (value) {
                    // Reminder settings implementation placeholder
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          value ? 'Reminders enabled' : 'Reminders disabled',
                        ),
                      ),
                    );
                  },
                ),
              ),
              ListTile(
                leading: const Icon(Icons.speed),
                title: const Text('Difficulty Level'),
                subtitle: const Text('Adjust learning difficulty'),
                trailing: DropdownButton<String>(
                  value: 'Medium',
                  items: ['Easy', 'Medium', 'Hard', 'Expert']
                      .map(
                        (level) =>
                            DropdownMenuItem(value: level, child: Text(level)),
                      )
                      .toList(),
                  onChanged: (value) {
                    // Difficulty setting implementation placeholder
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Difficulty set to: $value')),
                    );
                  },
                ),
              ),
              ListTile(
                leading: const Icon(Icons.psychology),
                title: const Text('AI Model'),
                subtitle: const Text('Choose AI model for tutoring'),
                trailing: const Text('GPT-4'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    // Enhanced help dialog with tips and tutorials
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help_outline, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Help & Tips'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '🎯 Getting Started:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• Create a learning plan for your subject'),
              const Text('• Generate flashcards for key concepts'),
              const Text('• Take adaptive quizzes to test knowledge'),
              const Text('• Track your progress over time'),
              const SizedBox(height: 16),
              const Text(
                '💡 Pro Tips:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• Review flashcards daily for best retention'),
              const Text('• Use different explanation styles'),
              const Text('• Focus on identified knowledge gaps'),
              const Text('• Set study reminders in settings'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showCreatePlanDialog() {
    // Enhanced create learning plan dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.add_circle, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Create Learning Plan'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('📚 Quick Start Options:'),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.calculate),
                title: const Text('Mathematics'),
                subtitle: const Text('Algebra, Calculus, Geometry'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showFeatureComingSoon('Math Learning Plan Creation');
                },
              ),
              ListTile(
                leading: const Icon(Icons.science),
                title: const Text('Science'),
                subtitle: const Text('Physics, Chemistry, Biology'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showFeatureComingSoon('Science Learning Plan Creation');
                },
              ),
              ListTile(
                leading: const Icon(Icons.language),
                title: const Text('Language Arts'),
                subtitle: const Text('Literature, Writing, Grammar'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showFeatureComingSoon('Language Learning Plan Creation');
                },
              ),
              ListTile(
                leading: const Icon(Icons.add),
                title: const Text('Custom Subject'),
                subtitle: const Text('Create your own learning plan'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showFeatureComingSoon('Custom Learning Plan Creation');
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showPracticeOptions() {
    // Enhanced practice options dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.play_arrow, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Practice Options'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('🎯 Choose your practice mode:'),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.style),
                title: const Text('Flashcard Review'),
                subtitle: const Text('Review your saved flashcards'),
                onTap: () {
                  Navigator.of(context).pop();
                  _tabController.animateTo(2); // Navigate to practice tab
                },
              ),
              ListTile(
                leading: const Icon(Icons.quiz),
                title: const Text('Adaptive Quiz'),
                subtitle: const Text('Take a personalized quiz'),
                onTap: () {
                  Navigator.of(context).pop();
                  _tabController.animateTo(2); // Navigate to practice tab
                },
              ),
              ListTile(
                leading: const Icon(Icons.psychology),
                title: const Text('Concept Explanation'),
                subtitle: const Text('Get AI explanations'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showFeatureComingSoon('Concept Explanation');
                },
              ),
              ListTile(
                leading: const Icon(Icons.analytics),
                title: const Text('Knowledge Gap Analysis'),
                subtitle: const Text('Identify areas to improve'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showFeatureComingSoon('Knowledge Gap Analysis');
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showFeatureComingSoon(String featureName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$featureName coming soon!'),
        backgroundColor: AppTheme.primaryGreen,
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  Widget _buildProgressStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              Icon(Icons.trending_up, color: color, size: 16),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivityList() {
    // TODO: Integrate with Firebase to fetch real user learning sessions and quiz results
    final activities = [
      {
        'title': 'Completed Math Quiz',
        'subtitle': 'Algebra - Score: 85%',
        'time': '2 hours ago',
        'icon': Icons.quiz,
        'color': Colors.green,
      },
      {
        'title': 'Reviewed Flashcards',
        'subtitle': 'Geometry - 15 cards',
        'time': '4 hours ago',
        'icon': Icons.style,
        'color': Colors.blue,
      },
      {
        'title': 'Started Learning Plan',
        'subtitle': 'Calculus Fundamentals',
        'time': '1 day ago',
        'icon': Icons.school,
        'color': Colors.purple,
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: (activity['color'] as Color).withValues(
                alpha: 0.1,
              ),
              child: Icon(
                activity['icon'] as IconData,
                color: activity['color'] as Color,
                size: 20,
              ),
            ),
            title: Text(
              activity['title'] as String,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(activity['subtitle'] as String),
            trailing: Text(
              activity['time'] as String,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            onTap: () {
              // TODO: Implement navigation to detailed activity view with performance analytics
              _showFeatureComingSoon('Activity Details');
            },
          ),
        );
      },
    );
  }

  void _showProgressFilters() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.filter_list, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Filter Progress Data'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Time Period:'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children:
                    ['Last 7 days', 'Last 30 days', 'Last 3 months', 'All time']
                        .map(
                          (period) => FilterChip(
                            label: Text(period),
                            selected: period == 'Last 7 days',
                            onSelected: (selected) {
                              // TODO: Implement time period filtering with data refresh
                            },
                          ),
                        )
                        .toList(),
              ),
              const SizedBox(height: 16),
              const Text('Subject:'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['All', 'Mathematics', 'Science', 'Language']
                    .map(
                      (subject) => FilterChip(
                        label: Text(subject),
                        selected: subject == 'All',
                        onSelected: (selected) {
                          // TODO: Implement subject-based filtering with multi-select support
                        },
                      ),
                    )
                    .toList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Apply selected filters and refresh progress data with loading state
              _showFeatureComingSoon('Progress Filtering');
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _exportProgressData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.download, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Export Progress Data'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Choose export format:'),
            SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.table_chart),
              title: Text('CSV File'),
              subtitle: Text('Spreadsheet compatible format'),
            ),
            ListTile(
              leading: Icon(Icons.picture_as_pdf),
              title: Text('PDF Report'),
              subtitle: Text('Formatted progress report'),
            ),
            ListTile(
              leading: Icon(Icons.code),
              title: Text('JSON Data'),
              subtitle: Text('Raw data format'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement CSV/PDF export with user progress analytics and charts
              _showFeatureComingSoon('Data Export');
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }
}
