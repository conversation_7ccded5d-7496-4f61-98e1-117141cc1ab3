import 'dart:convert';
import 'dart:developer' as developer;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../entities/flashcard.dart';
import '../entities/quiz.dart';
import '../entities/learning_progress.dart';
import '../repositories/ai_tutor_repository.dart'; // For ExplanationStyle
import '../../../../util/util.dart';
import '../../../../util/util_api_usage.dart';

import '../../../../models/usage.dart';

/// Service for generating AI content using existing CallChatAPI pattern
/// Integrates with the app's usage tracking and API management system
class AIContentService {
  static const String _serviceName = 'AIContentService';

  AIContentService() {
    developer.log(
      'AIContentService initialized with CallChatAPI integration',
      name: _serviceName,
    );
  }

  /// Gets the tutor usage type for API calls
  Usage get _tutorUsage {
    // Return default tutor usage - TODO: integrate with existing usage constants
    return Usage(
      onscreenMessage: 'AI Tutor',
      type: UsageType.tutor,
      icon: Icons.school,
      imagePath: "assets/page_icons/ai_tutor.png",
      systemPromptMessage:
          'You are an expert AI tutor specialized in personalized learning.',
      maxTokens: 2000,
    );
  }

  /// Tracks API usage for AI tutor operations
  Future<void> _trackApiUsage({
    required String prompt,
    required String response,
    required String operation,
    required String userId,
  }) async {
    try {
      final responseId = const Uuid().v4();
      await Util_API_Usage.saveApiUsage(
        responseId: responseId,
        apiUrl: 'ai_tutor/$operation',
        usageType: 'UsageType.tutor',
        questionContent: prompt,
        responseMessage: response,
        currentUserId: userId,
        conversationId: userId,
        question: prompt,
        model: 'ai_tutor_$operation',
      );
    } catch (e) {
      developer.log(
        'Failed to track API usage for $operation: $e',
        name: _serviceName,
      );
    }
  }

  /// Generates a learning plan using AI with CallChatAPI integration
  Future<LearningPlan> generateLearningPlan({
    required String subject,
    required String currentLevel,
    required List<String> learningGoals,
    required Map<String, dynamic> preferences,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check API usage limits before making the call
      if (await Util_API_Usage.isLimitExceeded(_tutorUsage, user.uid)) {
        developer.log(
          'API usage limit exceeded for learning plan generation',
          name: _serviceName,
        );
        return _createMockLearningPlan(subject, currentLevel, learningGoals);
      }

      final prompt = _buildLearningPlanPrompt(
        subject: subject,
        currentLevel: currentLevel,
        learningGoals: learningGoals,
        preferences: preferences,
      );

      // Use CallChatAPI for AI generation
      final response = await Util.CallChatAPI(
        prompt,
        _tutorUsage,
        [], // No conversation history for learning plan generation
        user.uid,
        user.uid,
      );

      // Track API usage
      await _trackApiUsage(
        prompt: prompt,
        response: response,
        operation: 'learning_plan_generation',
        userId: user.uid,
      );

      // Parse structured JSON from AI response for production use
      try {
        final jsonResponse = _extractJsonFromResponse(response);
        if (jsonResponse != null && jsonResponse is Map<String, dynamic>) {
          return _createLearningPlanFromJson(
            jsonResponse,
            subject,
            currentLevel,
            learningGoals,
          );
        }
      } catch (parseError) {
        developer.log(
          'Failed to parse AI response as JSON: $parseError',
          name: _serviceName,
          error: parseError,
        );
      }

      // Fallback to enhanced learning plan with AI content
      return _createEnhancedLearningPlan(
        subject,
        currentLevel,
        learningGoals,
        response,
      );
    } catch (e) {
      developer.log(
        'Error generating learning plan: $e',
        name: _serviceName,
        error: e,
      );
      // Return a basic learning plan as fallback
      return _createMockLearningPlan(subject, currentLevel, learningGoals);
    }
  }

  /// Generates flashcards using AI with CallChatAPI integration
  Future<List<Flashcard>> generateFlashcards({
    required String topic,
    required int count,
    required DifficultyLevel difficulty,
    String? context,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check API usage limits
      if (await Util_API_Usage.isLimitExceeded(_tutorUsage, user.uid)) {
        developer.log(
          'API usage limit exceeded for flashcard generation',
          name: _serviceName,
        );
        return _createMockFlashcards(topic, count, difficulty);
      }

      final prompt = _buildFlashcardPrompt(
        topic: topic,
        count: count,
        difficulty: difficulty,
        context: context,
      );

      // Use CallChatAPI for AI generation
      final response = await Util.CallChatAPI(
        prompt,
        _tutorUsage,
        [],
        user.uid,
        user.uid,
      );

      // Track API usage
      await _trackApiUsage(
        prompt: prompt,
        response: response,
        operation: 'flashcard_generation',
        userId: user.uid,
      );

      // Parse structured JSON from AI response
      try {
        final jsonResponse = _extractJsonFromResponse(response);
        if (jsonResponse != null) {
          List<dynamic>? flashcardsJson;

          // Check if response is directly an array of flashcards
          if (jsonResponse is List) {
            flashcardsJson = jsonResponse;
          } else if (jsonResponse is Map<String, dynamic> &&
              jsonResponse.containsKey('flashcards')) {
            flashcardsJson = jsonResponse['flashcards'] as List<dynamic>?;
          }

          if (flashcardsJson != null) {
            return _createFlashcardsFromJson(flashcardsJson, topic, difficulty);
          }
        }
      } catch (parseError) {
        developer.log(
          'Failed to parse flashcard JSON: $parseError',
          name: _serviceName,
          error: parseError,
        );
      }

      return _createEnhancedFlashcards(topic, count, difficulty, response);
    } catch (e) {
      developer.log(
        'Error generating flashcards: $e',
        name: _serviceName,
        error: e,
      );
      return _createMockFlashcards(topic, count, difficulty);
    }
  }

  /// Generates a quiz using AI with CallChatAPI integration
  Future<Quiz> generateQuiz({
    required String topic,
    required List<String> concepts,
    required DifficultyLevel difficulty,
    int questionCount = 5,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check API usage limits
      if (await Util_API_Usage.isLimitExceeded(_tutorUsage, user.uid)) {
        developer.log(
          'API usage limit exceeded for quiz generation',
          name: _serviceName,
        );
        return _createMockQuiz(topic, concepts, difficulty);
      }

      final prompt = _buildQuizPrompt(
        topic: topic,
        concepts: concepts,
        difficulty: difficulty,
        questionCount: questionCount,
      );

      // Use CallChatAPI for AI generation
      final response = await Util.CallChatAPI(
        prompt,
        _tutorUsage,
        [],
        user.uid,
        user.uid,
      );

      // Track API usage
      await _trackApiUsage(
        prompt: prompt,
        response: response,
        operation: 'quiz_generation',
        userId: user.uid,
      );

      // Parse structured JSON from AI response
      try {
        final jsonResponse = _extractJsonFromResponse(response);
        if (jsonResponse != null && jsonResponse is Map<String, dynamic>) {
          return _createQuizFromJson(jsonResponse, topic, concepts, difficulty);
        }
      } catch (parseError) {
        developer.log(
          'Failed to parse quiz JSON: $parseError',
          name: _serviceName,
          error: parseError,
        );
      }

      return _createEnhancedQuiz(topic, concepts, difficulty, response);
    } catch (e) {
      developer.log('Error generating quiz: $e', name: _serviceName, error: e);
      return _createMockQuiz(topic, concepts, difficulty);
    }
  }

  /// Explains a concept using AI with CallChatAPI integration
  Future<String> explainConcept({
    required String concept,
    required String context,
    required ExplanationStyle style,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check API usage limits
      if (await Util_API_Usage.isLimitExceeded(_tutorUsage, user.uid)) {
        developer.log(
          'API usage limit exceeded for concept explanation',
          name: _serviceName,
        );
        return _createMockExplanation(concept, style);
      }

      final prompt = _buildExplanationPrompt(
        concept: concept,
        context: context,
        style: style,
      );

      // Use CallChatAPI for AI generation
      final response = await Util.CallChatAPI(
        prompt,
        _tutorUsage,
        [],
        user.uid,
        user.uid,
      );

      // Track API usage
      await _trackApiUsage(
        prompt: prompt,
        response: response,
        operation: 'concept_explanation',
        userId: user.uid,
      );

      return _createEnhancedExplanation(concept, style, response);
    } catch (e) {
      developer.log(
        'Error explaining concept: $e',
        name: _serviceName,
        error: e,
      );
      return _createMockExplanation(concept, style);
    }
  }

  // Helper methods for building prompts

  /// Builds a learning plan prompt
  String _buildLearningPlanPrompt({
    required String subject,
    required String currentLevel,
    required List<String> learningGoals,
    required Map<String, dynamic> preferences,
  }) {
    return '''You are an expert educational AI tutor. Create a comprehensive learning plan for the following:

Subject: $subject
Current Level: $currentLevel
Learning Goals: ${learningGoals.join(', ')}
Preferences: ${preferences.toString()}

Create a detailed learning plan with:
1. Title and description
2. Milestones with target dates
3. Concepts to learn for each milestone
4. Resources and activities
5. Estimated duration (in days)

Return the response in JSON format with this structure:
{
  "title": "Learning Plan Title",
  "description": "Detailed description",
  "totalDuration": 30,
  "difficulty": "intermediate",
  "milestones": [
    {
      "title": "Milestone Title",
      "description": "Milestone description",
      "concepts": ["concept1", "concept2"],
      "resources": ["resource1", "resource2"],
      "estimatedDays": 7
    }
  ]
}''';
  }

  /// Builds a flashcard generation prompt
  String _buildFlashcardPrompt({
    required String topic,
    required int count,
    required DifficultyLevel difficulty,
    String? context,
  }) {
    return '''You are an expert educational content creator. Generate $count flashcards for the topic: $topic

Difficulty Level: ${difficulty.displayName.toLowerCase()}
Context: ${context ?? 'General learning context'}

Each flashcard should have:
- A clear, concise question on the front
- A comprehensive answer on the back
- Appropriate tags for categorization
- Difficulty level

Format each flashcard as JSON:
{
  "front": "Question text",
  "back": "Answer text with explanation",
  "tags": ["tag1", "tag2"],
  "difficulty": "easy|medium|hard|expert"
}

Return an array of $count flashcards in JSON format.''';
  }

  /// Builds a quiz generation prompt
  String _buildQuizPrompt({
    required String topic,
    required List<String> concepts,
    required DifficultyLevel difficulty,
    int questionCount = 5,
  }) {
    return '''You are an expert quiz creator. Generate an adaptive quiz for:

Topic: $topic
Concepts: ${concepts.join(', ')}
Difficulty: ${difficulty.displayName.toLowerCase()}
Number of Questions: $questionCount

Create questions that test understanding of the concepts. Include:
- Multiple choice questions with 4 options
- Clear explanations for correct answers
- Appropriate difficulty progression

Return JSON format:
{
  "title": "Quiz Title",
  "questions": [
    {
      "question": "Question text",
      "type": "multiple_choice",
      "options": ["A", "B", "C", "D"],
      "correctAnswers": ["A"],
      "explanation": "Why this answer is correct",
      "concept": "related concept",
      "points": 10
    }
  ]
}''';
  }

  /// Builds a concept explanation prompt
  String _buildExplanationPrompt({
    required String concept,
    required String context,
    required ExplanationStyle style,
  }) {
    return '''You are an expert tutor. Explain the concept: $concept

Context: $context
Explanation Style: ${style.displayName.toLowerCase()}

Provide a clear, comprehensive explanation that:
- Matches the requested style (simple, detailed, analogy, step-by-step, visual)
- Uses appropriate examples and analogies
- Is suitable for the learner's level
- Includes practical applications when relevant

Return a well-structured explanation.''';
  }

  // Helper methods for parsing and creating content

  /// Extracts JSON from AI response content
  dynamic _extractJsonFromResponse(String content) {
    try {
      // Try to find JSON in the response (object or array)
      final jsonStart = content.indexOf(RegExp(r'[{\[]'));
      final jsonEnd = content.lastIndexOf(RegExp(r'[}\]]'));

      if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
        final jsonString = content.substring(jsonStart, jsonEnd + 1);
        return json.decode(jsonString);
      }

      // If no JSON found, try to parse the entire content
      return json.decode(content);
    } catch (e) {
      developer.log(
        'Failed to extract JSON from response: $e',
        name: _serviceName,
        error: e,
      );
      return null;
    }
  }

  // Helper methods for creating content from JSON responses

  /// Creates a learning plan from JSON response
  LearningPlan _createLearningPlanFromJson(
    Map<String, dynamic> json,
    String subject,
    String currentLevel,
    List<String> learningGoals,
  ) {
    try {
      final user = FirebaseAuth.instance.currentUser;
      final userId = user?.uid ?? 'anonymous';

      final milestones =
          (json['milestones'] as List<dynamic>?)
              ?.map(
                (m) => LearningMilestone(
                  id: const Uuid().v4(),
                  title: m['title'] ?? 'Milestone',
                  description: m['description'] ?? 'Learning milestone',
                  concepts: List<String>.from(m['concepts'] ?? []),
                  targetDate: DateTime.now().add(
                    Duration(days: m['estimatedDays'] ?? 7),
                  ),
                  resources: List<String>.from(m['resources'] ?? []),
                  isCompleted: false,
                  completedAt: null,
                  metadata: m,
                ),
              )
              .toList() ??
          [];

      return LearningPlan(
        id: const Uuid().v4(),
        userId: userId,
        subject: subject,
        title: json['title'] ?? '$subject Learning Plan',
        description: json['description'] ?? 'AI-generated learning plan',
        milestones: milestones,
        startDate: DateTime.now(),
        targetEndDate: DateTime.now().add(
          Duration(days: json['totalDuration'] ?? 30),
        ),
        difficulty:
            _parseDifficultyFromString(json['difficulty']) ??
            DifficultyLevel.medium,
        learningGoals: learningGoals,
        preferences: {'currentLevel': currentLevel},
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      developer.log(
        'Error creating learning plan from JSON: $e',
        name: _serviceName,
      );
      return _createMockLearningPlan(subject, currentLevel, learningGoals);
    }
  }

  /// Creates flashcards from JSON response
  List<Flashcard> _createFlashcardsFromJson(
    List<dynamic> json,
    String topic,
    DifficultyLevel difficulty,
  ) {
    try {
      return json.map((item) {
        final flashcardData = item as Map<String, dynamic>;
        return Flashcard(
          id: const Uuid().v4(),
          front: flashcardData['front'] ?? 'Question',
          back: flashcardData['back'] ?? 'Answer',
          subject: topic,
          topic: topic,
          tags: List<String>.from(flashcardData['tags'] ?? []),
          difficulty:
              _parseDifficultyFromString(flashcardData['difficulty']) ??
              difficulty,
          createdAt: DateTime.now(),
          lastReviewed: DateTime.now(),
          nextReview: DateTime.now(),
          reviewCount: 0,
          easeFactor: 2.5,
          interval: 1,
        );
      }).toList();
    } catch (e) {
      developer.log(
        'Error creating flashcards from JSON: $e',
        name: _serviceName,
      );
      return _createMockFlashcards(topic, json.length, difficulty);
    }
  }

  /// Creates a quiz from JSON response
  Quiz _createQuizFromJson(
    Map<String, dynamic> json,
    String topic,
    List<String> concepts,
    DifficultyLevel difficulty,
  ) {
    try {
      final questions =
          (json['questions'] as List<dynamic>?)
              ?.map(
                (q) => QuizQuestion(
                  id: const Uuid().v4(),
                  question: q['question'] ?? 'Question',
                  type: _parseQuestionType(q['type']),
                  options: List<String>.from(q['options'] ?? []),
                  correctAnswers: List<String>.from(q['correctAnswers'] ?? []),
                  explanation: q['explanation'] ?? 'Explanation',
                  concept: q['concept'] ?? concepts.first,
                  points: q['points'] ?? 10,
                  difficulty: difficulty,
                ),
              )
              .toList() ??
          [];

      return Quiz(
        id: const Uuid().v4(),
        title: json['title'] ?? '$topic Quiz',
        subject: topic,
        topic: topic,
        questions: questions,
        difficulty: difficulty,
        createdAt: DateTime.now(),
        timeLimit: json['timeLimit'] ?? 30,
        isAdaptive: json['isAdaptive'] ?? false,
        metadata: json,
      );
    } catch (e) {
      developer.log('Error creating quiz from JSON: $e', name: _serviceName);
      return _createMockQuiz(topic, concepts, difficulty);
    }
  }

  // Helper methods for creating enhanced content with AI responses

  /// Creates an enhanced learning plan with AI content
  LearningPlan _createEnhancedLearningPlan(
    String subject,
    String currentLevel,
    List<String> learningGoals,
    String aiResponse,
  ) {
    // TODO: Parse AI response and extract meaningful content for learning plan
    // For now, create a structured plan with AI-enhanced descriptions
    final user = FirebaseAuth.instance.currentUser;
    final userId = user?.uid ?? 'anonymous';

    final milestones = learningGoals
        .map(
          (goal) => LearningMilestone(
            id: const Uuid().v4(),
            title: 'Master $goal',
            description:
                'AI-enhanced milestone: ${aiResponse.substring(0, aiResponse.length > 100 ? 100 : aiResponse.length)}...',
            concepts: [goal],
            targetDate: DateTime.now().add(const Duration(days: 14)),
            resources: ['AI-generated resources for $goal'],
            isCompleted: false,
            completedAt: null,
            metadata: {'aiGenerated': true},
          ),
        )
        .toList();

    return LearningPlan(
      id: const Uuid().v4(),
      userId: userId,
      subject: subject,
      title: '$subject Learning Plan (AI Enhanced)',
      description:
          'AI-generated learning plan: ${aiResponse.substring(0, aiResponse.length > 200 ? 200 : aiResponse.length)}...',
      milestones: milestones,
      startDate: DateTime.now(),
      targetEndDate: DateTime.now().add(
        Duration(days: learningGoals.length * 14),
      ),
      difficulty: DifficultyLevel.medium,
      learningGoals: learningGoals,
      preferences: {'currentLevel': currentLevel},
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
    );
  }

  /// Creates enhanced flashcards with AI content
  List<Flashcard> _createEnhancedFlashcards(
    String topic,
    int count,
    DifficultyLevel difficulty,
    String aiResponse,
  ) {
    // TODO: Parse AI response and extract meaningful content for flashcards
    return List.generate(
      count,
      (index) => Flashcard(
        id: const Uuid().v4(),
        front: 'AI-generated question ${index + 1} for $topic',
        back:
            'AI-enhanced answer: ${aiResponse.substring(0, aiResponse.length > 100 ? 100 : aiResponse.length)}...',
        subject: topic,
        topic: topic,
        tags: [topic, 'ai-generated'],
        difficulty: difficulty,
        createdAt: DateTime.now(),
        lastReviewed: DateTime.now(),
        nextReview: DateTime.now().add(const Duration(days: 1)),
        reviewCount: 0,
        easeFactor: 2.5,
        interval: 1,
      ),
    );
  }

  /// Creates an enhanced quiz with AI content
  Quiz _createEnhancedQuiz(
    String topic,
    List<String> concepts,
    DifficultyLevel difficulty,
    String aiResponse,
  ) {
    // TODO: Parse AI response and extract meaningful content for quiz
    final questions = concepts
        .take(5)
        .map(
          (concept) => QuizQuestion(
            id: const Uuid().v4(),
            question: 'AI-generated question about $concept',
            type: QuestionType.multipleChoice,
            options: ['Option A', 'Option B', 'Option C', 'Option D'],
            correctAnswers: ['Option A'],
            explanation:
                'AI-enhanced explanation: ${aiResponse.substring(0, aiResponse.length > 100 ? 100 : aiResponse.length)}...',
            concept: concept,
            points: 10,
            difficulty: difficulty,
          ),
        )
        .toList();

    return Quiz(
      id: const Uuid().v4(),
      title: '$topic Quiz (AI Enhanced)',
      subject: topic,
      topic: topic,
      questions: questions,
      difficulty: difficulty,
      createdAt: DateTime.now(),
      timeLimit: 30,
      isAdaptive: false,
      metadata: {'aiResponse': aiResponse},
    );
  }

  /// Creates an enhanced explanation with AI content
  String _createEnhancedExplanation(
    String concept,
    ExplanationStyle style,
    String aiResponse,
  ) {
    // Return the AI response directly as it should already be a good explanation
    return aiResponse.isNotEmpty
        ? aiResponse
        : _createMockExplanation(concept, style);
  }

  // Helper methods for parsing and creating mock content

  /// Parses difficulty level from string
  DifficultyLevel? _parseDifficultyFromString(String? difficultyStr) {
    if (difficultyStr == null) return null;

    switch (difficultyStr.toLowerCase()) {
      case 'easy':
        return DifficultyLevel.easy;
      case 'medium':
        return DifficultyLevel.medium;
      case 'hard':
        return DifficultyLevel.hard;
      case 'expert':
        return DifficultyLevel.expert;
      default:
        return null;
    }
  }

  /// Parses question type from string
  QuestionType _parseQuestionType(String? typeStr) {
    if (typeStr == null) return QuestionType.multipleChoice;

    switch (typeStr.toLowerCase()) {
      case 'multiple_choice':
        return QuestionType.multipleChoice;
      case 'true_false':
        return QuestionType.trueFalse;
      case 'fill_in_blank':
        return QuestionType.fillInBlank;
      case 'short_answer':
        return QuestionType.shortAnswer;
      case 'essay':
        return QuestionType.essay;
      default:
        return QuestionType.multipleChoice;
    }
  }

  // Mock content creation methods for fallback scenarios

  /// Creates a mock learning plan when AI generation fails
  LearningPlan _createMockLearningPlan(
    String subject,
    String currentLevel,
    List<String> learningGoals,
  ) {
    final user = FirebaseAuth.instance.currentUser;
    final userId = user?.uid ?? 'anonymous';

    final milestones = learningGoals
        .map(
          (goal) => LearningMilestone(
            id: const Uuid().v4(),
            title: 'Master $goal',
            description:
                'Learn the fundamentals of $goal and apply them in practice.',
            concepts: [goal],
            targetDate: DateTime.now().add(const Duration(days: 14)),
            isCompleted: false,
            completedAt: null,
            resources: ['Study materials for $goal', 'Practice exercises'],
            metadata: {'mockData': true},
          ),
        )
        .toList();

    return LearningPlan(
      id: const Uuid().v4(),
      userId: userId,
      subject: subject,
      title: '$subject Learning Plan',
      description: 'A structured learning plan to master $subject concepts.',
      milestones: milestones,
      startDate: DateTime.now(),
      targetEndDate: DateTime.now().add(
        Duration(days: learningGoals.length * 14),
      ),
      difficulty: DifficultyLevel.medium,
      learningGoals: learningGoals,
      preferences: {'currentLevel': currentLevel},
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
    );
  }

  /// Creates mock flashcards when AI generation fails
  List<Flashcard> _createMockFlashcards(
    String topic,
    int count,
    DifficultyLevel difficulty,
  ) {
    return List.generate(
      count,
      (index) => Flashcard(
        id: const Uuid().v4(),
        front: 'Sample question ${index + 1} about $topic',
        back: 'Sample answer explaining key concepts of $topic.',
        subject: topic,
        topic: topic,
        tags: [topic, 'sample'],
        difficulty: difficulty,
        createdAt: DateTime.now(),
        lastReviewed: DateTime.now(),
        nextReview: DateTime.now().add(const Duration(days: 1)),
        reviewCount: 0,
        easeFactor: 2.5,
        interval: 1,
      ),
    );
  }

  /// Creates a mock quiz when AI generation fails
  Quiz _createMockQuiz(
    String topic,
    List<String> concepts,
    DifficultyLevel difficulty,
  ) {
    final questions = concepts
        .take(5)
        .map(
          (concept) => QuizQuestion(
            id: const Uuid().v4(),
            question: 'What is the main principle of $concept?',
            type: QuestionType.multipleChoice,
            options: [
              'Option A: Basic principle',
              'Option B: Advanced concept',
              'Option C: Related topic',
              'Option D: None of the above',
            ],
            correctAnswers: ['Option A: Basic principle'],
            explanation:
                'The main principle of $concept involves understanding its core fundamentals.',
            concept: concept,
            points: 10,
            difficulty: difficulty,
          ),
        )
        .toList();

    return Quiz(
      id: const Uuid().v4(),
      title: '$topic Quiz',
      subject: topic,
      topic: topic,
      questions: questions,
      difficulty: difficulty,
      createdAt: DateTime.now(),
      timeLimit: 30,
      isAdaptive: false,
      metadata: {'mockData': true},
    );
  }

  /// Creates a mock explanation when AI generation fails
  String _createMockExplanation(String concept, ExplanationStyle style) {
    switch (style) {
      case ExplanationStyle.simple:
        return 'Simple explanation: $concept is a fundamental concept that helps you understand the basics.';
      case ExplanationStyle.detailed:
        return 'Detailed explanation: $concept involves multiple aspects including theoretical foundations, practical applications, and real-world examples. Understanding this concept requires careful study and practice.';
      case ExplanationStyle.analogy:
        return 'Think of $concept like a building - it has a foundation (basic principles), structure (main components), and purpose (practical applications).';
      case ExplanationStyle.stepByStep:
        return 'Step 1: First, understand the basic definition of $concept.\nStep 2: Learn the key components and how they relate.\nStep 3: Practice applying the concept in different scenarios.\nStep 4: Review and reinforce your understanding.';
      case ExplanationStyle.visual:
        return 'Imagine $concept as a visual diagram where each component connects to others, forming a comprehensive understanding of the topic.';
    }
  }
}
